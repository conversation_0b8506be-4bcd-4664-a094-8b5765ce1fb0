{"folders": [{"name": "Root", "path": "."}, {"name": "Website", "path": "./apps/websites/sahayaka.ai"}], "settings": {"typescript.preferences.includePackageJsonAutoImports": "on", "typescript.suggest.autoImports": true, "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "files.associations": {"*.css": "tailwindcss"}, "emmet.includeLanguages": {"javascript": "javascriptreact", "typescript": "typescriptreact"}, "tailwindCSS.includeLanguages": {"typescript": "typescript", "typescriptreact": "typescriptreact"}, "search.exclude": {"**/node_modules": true, "**/dist": true, "**/.next": true, "**/out": true, "**/.cache": true}, "files.exclude": {"**/node_modules": true, "**/.next": true, "**/out": true}}, "extensions": {"recommendations": ["bradlc.vscode-tailwindcss", "esbenp.prettier-vscode", "dbaeumer.vscode-eslint", "ms-vscode.vscode-typescript-next", "formulahendry.auto-rename-tag", "christian-kohler.path-intellisense"]}}