import type { Metada<PERSON> } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import Header from '@/components/Header'
import Footer from '@/components/Footer'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'Sahayaka - AI-Powered Healthcare Automation',
  description: 'Revolutionary AI agentic workflow automation for healthcare administrative tasks. Create comprehensive patient profiles, reduce clinician burden, and improve accuracy.',
  keywords: 'healthcare AI, workflow automation, patient profiles, clinical efficiency, administrative automation',
  authors: [{ name: 'Sahayaka Team' }],
  viewport: 'width=device-width, initial-scale=1',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <Header />
        <main className="min-h-screen">
          {children}
        </main>
        <Footer />
      </body>
    </html>
  )
}
