{"name": "isexe", "version": "2.0.0", "description": "Minimal module to check if a file is executable.", "main": "index.js", "directories": {"test": "test"}, "devDependencies": {"mkdirp": "^0.5.1", "rimraf": "^2.5.0", "tap": "^10.3.0"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "author": "<PERSON> <<EMAIL>> (http://blog.izs.me/)", "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/isaacs/isexe.git"}, "keywords": [], "bugs": {"url": "https://github.com/isaacs/isexe/issues"}, "homepage": "https://github.com/isaacs/isexe#readme"}