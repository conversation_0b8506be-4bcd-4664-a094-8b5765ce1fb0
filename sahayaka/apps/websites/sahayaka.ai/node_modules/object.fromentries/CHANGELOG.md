# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v2.0.8](https://github.com/es-shims/Object.fromEntries/compare/v2.0.7...v2.0.8) - 2024-03-18

### Commits

- [actions] remove redundant finisher [`70e8717`](https://github.com/es-shims/Object.fromEntries/commit/70e8717d348e7d2b919c48ce7ab8cdc403664250)
- [Deps] update `call-bind`, `define-properties`, `es-abstract` [`f4b1a93`](https://github.com/es-shims/Object.fromEntries/commit/f4b1a93c665224e02724d3984eac0ce8508407cc)
- [Refactor] use `es-object-atoms` where possible [`252ffa7`](https://github.com/es-shims/Object.fromEntries/commit/252ffa73b503b29a7751730f5bca52f5136469e2)
- [Dev Deps] update `aud`, `tape` [`200ccd5`](https://github.com/es-shims/Object.fromEntries/commit/200ccd563a4bf5a55603be7877646dae1f67675c)

## [v2.0.7](https://github.com/es-shims/Object.fromEntries/compare/v2.0.6...v2.0.7) - 2023-08-28

### Commits

- [Deps] update `define-properties`, `es-abstract` [`dbf1467`](https://github.com/es-shims/Object.fromEntries/commit/dbf1467c5586bbf0183ebdee1239176eaf1f94d6)
- [Dev Deps] update `@es-shims/api`, `@ljharb/eslint-config`, `aud`, `tape` [`495556f`](https://github.com/es-shims/Object.fromEntries/commit/495556fd641f1c6b9f2f1eedf1be98ed7dad1c43)

## [v2.0.6](https://github.com/es-shims/Object.fromEntries/compare/v2.0.5...v2.0.6) - 2022-11-06

### Commits

- [actions] reuse common workflows [`867603d`](https://github.com/es-shims/Object.fromEntries/commit/867603ddb384887d25749488579a4c74fa9c1443)
- [meta] add `auto-changelog` [`3621c90`](https://github.com/es-shims/Object.fromEntries/commit/3621c90294140f0139cf65c9ed852e7ace01c40f)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `@es-shims/api`, `safe-publish-latest`, `tape` [`d6c3de7`](https://github.com/es-shims/Object.fromEntries/commit/d6c3de793e1f2055b5b11102f778270378956f71)
- [Deps] update `define-properties`, `es-abstract` [`a0eca66`](https://github.com/es-shims/Object.fromEntries/commit/a0eca66c2d21c1030430b8c06c36d9235d0f5870)
- [actions] update rebase action to use reusable workflow [`91df159`](https://github.com/es-shims/Object.fromEntries/commit/91df159d076da80dcb62dd4a27cc303e4bdaf76e)
- [actions] update codecov uploader [`e7f2c96`](https://github.com/es-shims/Object.fromEntries/commit/e7f2c965103df04dc21042bd735ce1a5d1446193)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `tape` [`ec22968`](https://github.com/es-shims/Object.fromEntries/commit/ec22968b61d5888816c66d8416bb045f6584e3a1)

<!-- auto-changelog-above -->

2.0.5 / 2021-10-03
=================
  * [readme] add actions and codecov badges
  * [Deps] update `es-abstract`
  * [Deps] remove unused `has` dep
  * [meta] use `prepublishOnly` script for npm 7+
  * [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `@es-shims/api`, `aud`, `tape`
  * [actions] update workflows
  * [actions] use `node/install` instead of `node/run`; use `codecov` action
  * [Tests] increase coverage

2.0.4 / 2021-02-21
=================
  * [readme] fix repo URLs; remove travis badge
  * [meta] do not publish github action workflow files
  * [Deps] update `call-bind`, `es-abstract`
  * [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `has-strict-mode`, `tape`
  * [actions] update workflows

2.0.3 / 2020-11-26
=================
  * [Deps] update `es-abstract`; remove `function-bind`; use `call-bind` where applicable
  * [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `tape`; add `aud`, `safe-publish-latest`
  * [actions] add "Allow Edits" workflow
  * [actions] switch Automatic Rebase workflow to `pull_request_target` event
  * [Tests] migrate tests to Github Actions
  * [Tests] run `nyc` on all tests
  * [Tests] add `implementation` test; run `es-shim-api` in postlint; use `tape` runner
  * [Tests] only audit prod deps

2.0.2 / 2019-12-12
=================
  * [Refactor] use split-up `es-abstract` (63% bundle size decrease)
  * [readme] remove testling
  * [Dev Deps] update `eslint`, `@ljharb/eslint-config`
  * [meta] add `funding` field
  * [Tests] use shared travis-ci configs
  * [actions] add automatic rebasing / merge commit blocking

2.0.1 / 2019-10-03
=================
  * [Fix] do not mutate `Object.fromEntries` when already present
  * [Deps] update `define-properties`, `es-abstract`, `has`
  * [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `covert`, `tape`
  * [Tests] up to `node` `v12.9`, `v11.15`, `v10.16`, `v9.11`, `v8.16`, `v6.17`, `v4.9`
  * [Tests] use `npx aud` instead of `nsp` or `npm audit` with hoops

2.0.0 / 2018-08-09
=================
  * [Breaking] throw when `iterable` is nullish
  * [Docs] Fix link to proposed spec

1.0.0 / 2018-03-21
=================
  * v1.0.0
