/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fnandi%2FDocuments%2FNandiWork%2Frepos%2Fsahayaka%2Fapps%2Fwebsites%2Fsahayaka.ai%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fnandi%2FDocuments%2FNandiWork%2Frepos%2Fsahayaka%2Fapps%2Fwebsites%2Fsahayaka.ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fnandi%2FDocuments%2FNandiWork%2Frepos%2Fsahayaka%2Fapps%2Fwebsites%2Fsahayaka.ai%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fnandi%2FDocuments%2FNandiWork%2Frepos%2Fsahayaka%2Fapps%2Fwebsites%2Fsahayaka.ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?5bc9\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\")), \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/app/page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/app/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fnandi%2FDocuments%2FNandiWork%2Frepos%2Fsahayaka%2Fapps%2Fwebsites%2Fsahayaka.ai%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fnandi%2FDocuments%2FNandiWork%2Frepos%2Fsahayaka%2Fapps%2Fwebsites%2Fsahayaka.ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fnandi%2FDocuments%2FNandiWork%2Frepos%2Fsahayaka%2Fapps%2Fwebsites%2Fsahayaka.ai%2Fcomponents%2FHeader.tsx&modules=%2FUsers%2Fnandi%2FDocuments%2FNandiWork%2Frepos%2Fsahayaka%2Fapps%2Fwebsites%2Fsahayaka.ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js&modules=%2FUsers%2Fnandi%2FDocuments%2FNandiWork%2Frepos%2Fsahayaka%2Fapps%2Fwebsites%2Fsahayaka.ai%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22app%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fnandi%2FDocuments%2FNandiWork%2Frepos%2Fsahayaka%2Fapps%2Fwebsites%2Fsahayaka.ai%2Fapp%2Fglobals.css&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fnandi%2FDocuments%2FNandiWork%2Frepos%2Fsahayaka%2Fapps%2Fwebsites%2Fsahayaka.ai%2Fcomponents%2FHeader.tsx&modules=%2FUsers%2Fnandi%2FDocuments%2FNandiWork%2Frepos%2Fsahayaka%2Fapps%2Fwebsites%2Fsahayaka.ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js&modules=%2FUsers%2Fnandi%2FDocuments%2FNandiWork%2Frepos%2Fsahayaka%2Fapps%2Fwebsites%2Fsahayaka.ai%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22app%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fnandi%2FDocuments%2FNandiWork%2Frepos%2Fsahayaka%2Fapps%2Fwebsites%2Fsahayaka.ai%2Fapp%2Fglobals.css&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/Header.tsx */ \"(ssr)/./components/Header.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/next/dist/client/link.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZuYW5kaSUyRkRvY3VtZW50cyUyRk5hbmRpV29yayUyRnJlcG9zJTJGc2FoYXlha2ElMkZhcHBzJTJGd2Vic2l0ZXMlMkZzYWhheWFrYS5haSUyRmNvbXBvbmVudHMlMkZIZWFkZXIudHN4Jm1vZHVsZXM9JTJGVXNlcnMlMkZuYW5kaSUyRkRvY3VtZW50cyUyRk5hbmRpV29yayUyRnJlcG9zJTJGc2FoYXlha2ElMkZhcHBzJTJGd2Vic2l0ZXMlMkZzYWhheWFrYS5haSUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGbGluay5qcyZtb2R1bGVzPSUyRlVzZXJzJTJGbmFuZGklMkZEb2N1bWVudHMlMkZOYW5kaVdvcmslMkZyZXBvcyUyRnNhaGF5YWthJTJGYXBwcyUyRndlYnNpdGVzJTJGc2FoYXlha2EuYWklMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZm9udCUyRmdvb2dsZSUyRnRhcmdldC5jc3MlM0YlN0IlMjJwYXRoJTIyJTNBJTIyYXBwJTJGbGF5b3V0LnRzeCUyMiUyQyUyMmltcG9ydCUyMiUzQSUyMkludGVyJTIyJTJDJTIyYXJndW1lbnRzJTIyJTNBJTVCJTdCJTIyc3Vic2V0cyUyMiUzQSU1QiUyMmxhdGluJTIyJTVEJTdEJTVEJTJDJTIydmFyaWFibGVOYW1lJTIyJTNBJTIyaW50ZXIlMjIlN0QmbW9kdWxlcz0lMkZVc2VycyUyRm5hbmRpJTJGRG9jdW1lbnRzJTJGTmFuZGlXb3JrJTJGcmVwb3MlMkZzYWhheWFrYSUyRmFwcHMlMkZ3ZWJzaXRlcyUyRnNhaGF5YWthLmFpJTJGYXBwJTJGZ2xvYmFscy5jc3Mmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDBKQUFvSTtBQUNwSSIsInNvdXJjZXMiOlsid2VicGFjazovL3NhaGF5YWthLz84MmYxIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL25hbmRpL0RvY3VtZW50cy9OYW5kaVdvcmsvcmVwb3Mvc2FoYXlha2EvYXBwcy93ZWJzaXRlcy9zYWhheWFrYS5haS9jb21wb25lbnRzL0hlYWRlci50c3hcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9uYW5kaS9Eb2N1bWVudHMvTmFuZGlXb3JrL3JlcG9zL3NhaGF5YWthL2FwcHMvd2Vic2l0ZXMvc2FoYXlha2EuYWkvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvbGluay5qc1wiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fnandi%2FDocuments%2FNandiWork%2Frepos%2Fsahayaka%2Fapps%2Fwebsites%2Fsahayaka.ai%2Fcomponents%2FHeader.tsx&modules=%2FUsers%2Fnandi%2FDocuments%2FNandiWork%2Frepos%2Fsahayaka%2Fapps%2Fwebsites%2Fsahayaka.ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js&modules=%2FUsers%2Fnandi%2FDocuments%2FNandiWork%2Frepos%2Fsahayaka%2Fapps%2Fwebsites%2Fsahayaka.ai%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22app%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fnandi%2FDocuments%2FNandiWork%2Frepos%2Fsahayaka%2Fapps%2Fwebsites%2Fsahayaka.ai%2Fapp%2Fglobals.css&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fnandi%2FDocuments%2FNandiWork%2Frepos%2Fsahayaka%2Fapps%2Fwebsites%2Fsahayaka.ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fnandi%2FDocuments%2FNandiWork%2Frepos%2Fsahayaka%2Fapps%2Fwebsites%2Fsahayaka.ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fnandi%2FDocuments%2FNandiWork%2Frepos%2Fsahayaka%2Fapps%2Fwebsites%2Fsahayaka.ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fnandi%2FDocuments%2FNandiWork%2Frepos%2Fsahayaka%2Fapps%2Fwebsites%2Fsahayaka.ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fnandi%2FDocuments%2FNandiWork%2Frepos%2Fsahayaka%2Fapps%2Fwebsites%2Fsahayaka.ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fnandi%2FDocuments%2FNandiWork%2Frepos%2Fsahayaka%2Fapps%2Fwebsites%2Fsahayaka.ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fnandi%2FDocuments%2FNandiWork%2Frepos%2Fsahayaka%2Fapps%2Fwebsites%2Fsahayaka.ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fnandi%2FDocuments%2FNandiWork%2Frepos%2Fsahayaka%2Fapps%2Fwebsites%2Fsahayaka.ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fnandi%2FDocuments%2FNandiWork%2Frepos%2Fsahayaka%2Fapps%2Fwebsites%2Fsahayaka.ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fnandi%2FDocuments%2FNandiWork%2Frepos%2Fsahayaka%2Fapps%2Fwebsites%2Fsahayaka.ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fnandi%2FDocuments%2FNandiWork%2Frepos%2Fsahayaka%2Fapps%2Fwebsites%2Fsahayaka.ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fnandi%2FDocuments%2FNandiWork%2Frepos%2Fsahayaka%2Fapps%2Fwebsites%2Fsahayaka.ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fnandi%2FDocuments%2FNandiWork%2Frepos%2Fsahayaka%2Fapps%2Fwebsites%2Fsahayaka.ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fnandi%2FDocuments%2FNandiWork%2Frepos%2Fsahayaka%2Fapps%2Fwebsites%2Fsahayaka.ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fnandi%2FDocuments%2FNandiWork%2Frepos%2Fsahayaka%2Fapps%2Fwebsites%2Fsahayaka.ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fnandi%2FDocuments%2FNandiWork%2Frepos%2Fsahayaka%2Fapps%2Fwebsites%2Fsahayaka.ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fnandi%2FDocuments%2FNandiWork%2Frepos%2Fsahayaka%2Fapps%2Fwebsites%2Fsahayaka.ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fnandi%2FDocuments%2FNandiWork%2Frepos%2Fsahayaka%2Fapps%2Fwebsites%2Fsahayaka.ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fnandi%2FDocuments%2FNandiWork%2Frepos%2Fsahayaka%2Fapps%2Fwebsites%2Fsahayaka.ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fnandi%2FDocuments%2FNandiWork%2Frepos%2Fsahayaka%2Fapps%2Fwebsites%2Fsahayaka.ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/next/dist/client/link.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZuYW5kaSUyRkRvY3VtZW50cyUyRk5hbmRpV29yayUyRnJlcG9zJTJGc2FoYXlha2ElMkZhcHBzJTJGd2Vic2l0ZXMlMkZzYWhheWFrYS5haSUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGbGluay5qcyZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zYWhheWFrYS8/ZmZlZSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9uYW5kaS9Eb2N1bWVudHMvTmFuZGlXb3JrL3JlcG9zL3NhaGF5YWthL2FwcHMvd2Vic2l0ZXMvc2FoYXlha2EuYWkvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvbGluay5qc1wiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fnandi%2FDocuments%2FNandiWork%2Frepos%2Fsahayaka%2Fapps%2Fwebsites%2Fsahayaka.ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./components/Header.tsx":
/*!*******************************!*\
  !*** ./components/Header.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction Header() {\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const navigation = [\n        {\n            name: \"Home\",\n            href: \"/\"\n        },\n        {\n            name: \"About\",\n            href: \"/about\"\n        },\n        {\n            name: \"Features\",\n            href: \"/features\"\n        },\n        {\n            name: \"Solutions\",\n            href: \"/solutions\"\n        },\n        {\n            name: \"Contact\",\n            href: \"/contact\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"bg-white shadow-sm sticky top-0 z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n            className: \"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\",\n            \"aria-label\": \"Top\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex w-full items-center justify-between border-b border-indigo-500 py-6 lg:border-none\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/\",\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-8 w-8 healthcare-gradient rounded-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-white font-bold text-lg\",\n                                            children: \"S\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Header.tsx\",\n                                            lineNumber: 24,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Header.tsx\",\n                                        lineNumber: 23,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: \"Sahayaka\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Header.tsx\",\n                                        lineNumber: 26,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Header.tsx\",\n                                lineNumber: 22,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Header.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"ml-10 hidden space-x-8 lg:block\",\n                            children: navigation.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: link.href,\n                                    className: \"text-base font-medium text-gray-500 hover:text-gray-900 transition-colors\",\n                                    children: link.name\n                                }, link.name, false, {\n                                    fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Header.tsx\",\n                                    lineNumber: 32,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Header.tsx\",\n                            lineNumber: 30,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"ml-10 hidden lg:block\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/contact\",\n                                className: \"inline-block bg-indigo-500 py-2 px-4 border border-transparent rounded-md text-base font-medium text-white hover:bg-opacity-75 transition-all\",\n                                children: \"Get Started\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Header.tsx\",\n                                lineNumber: 43,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Header.tsx\",\n                            lineNumber: 42,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                className: \"bg-white rounded-md p-2 inline-flex items-center justify-center text-gray-400 hover:text-gray-500 hover:bg-gray-100\",\n                                onClick: ()=>setIsMenuOpen(!isMenuOpen),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"sr-only\",\n                                        children: \"Open main menu\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Header.tsx\",\n                                        lineNumber: 58,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"h-6 w-6\",\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        fill: \"none\",\n                                        viewBox: \"0 0 24 24\",\n                                        stroke: \"currentColor\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M4 6h16M4 12h16M4 18h16\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Header.tsx\",\n                                            lineNumber: 66,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Header.tsx\",\n                                        lineNumber: 59,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Header.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Header.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Header.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 9\n                }, this),\n                isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"lg:hidden\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-2 pt-2 pb-3 space-y-1\",\n                        children: [\n                            navigation.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: link.href,\n                                    className: \"block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-50\",\n                                    onClick: ()=>setIsMenuOpen(false),\n                                    children: link.name\n                                }, link.name, false, {\n                                    fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Header.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 17\n                                }, this)),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/contact\",\n                                className: \"block px-3 py-2 rounded-md text-base font-medium bg-indigo-500 text-white hover:bg-indigo-600\",\n                                onClick: ()=>setIsMenuOpen(false),\n                                children: \"Get Started\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Header.tsx\",\n                                lineNumber: 91,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Header.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Header.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Header.tsx\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Header.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Header.tsx\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"e8636fdf61f3\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zYWhheWFrYS8uL2FwcC9nbG9iYWxzLmNzcz8yYmU5Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiZTg2MzZmZGY2MWYzXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Header */ \"(rsc)/./components/Header.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Footer */ \"(rsc)/./components/Footer.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: \"Sahayaka - AI-Powered Healthcare Automation\",\n    description: \"Revolutionary AI agentic workflow automation for healthcare administrative tasks. Create comprehensive patient profiles, reduce clinician burden, and improve accuracy.\",\n    keywords: \"healthcare AI, workflow automation, patient profiles, clinical efficiency, administrative automation\",\n    authors: [\n        {\n            name: \"Sahayaka Team\"\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/app/layout.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"min-h-screen\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/app/layout.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/app/layout.tsx\",\n                    lineNumber: 29,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/app/layout.tsx\",\n            lineNumber: 24,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/app/layout.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_Hero__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Hero */ \"(rsc)/./components/Hero.tsx\");\n/* harmony import */ var _components_Features__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Features */ \"(rsc)/./components/Features.tsx\");\n/* harmony import */ var _components_Solutions__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Solutions */ \"(rsc)/./components/Solutions.tsx\");\n/* harmony import */ var _components_Stats__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Stats */ \"(rsc)/./components/Stats.tsx\");\n/* harmony import */ var _components_CTA__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/CTA */ \"(rsc)/./components/CTA.tsx\");\n\n\n\n\n\n\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Hero__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/app/page.tsx\",\n                lineNumber: 10,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Features__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/app/page.tsx\",\n                lineNumber: 11,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Stats__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/app/page.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Solutions__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/app/page.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CTA__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/app/page.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBQW9DO0FBQ1E7QUFDRTtBQUNSO0FBQ0o7QUFFbkIsU0FBU0s7SUFDdEIscUJBQ0U7OzBCQUNFLDhEQUFDTCx3REFBSUE7Ozs7OzBCQUNMLDhEQUFDQyw0REFBUUE7Ozs7OzBCQUNULDhEQUFDRSx5REFBS0E7Ozs7OzBCQUNOLDhEQUFDRCw2REFBU0E7Ozs7OzBCQUNWLDhEQUFDRSx1REFBR0E7Ozs7Ozs7QUFHViIsInNvdXJjZXMiOlsid2VicGFjazovL3NhaGF5YWthLy4vYXBwL3BhZ2UudHN4Pzc2MDMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IEhlcm8gZnJvbSAnQC9jb21wb25lbnRzL0hlcm8nXG5pbXBvcnQgRmVhdHVyZXMgZnJvbSAnQC9jb21wb25lbnRzL0ZlYXR1cmVzJ1xuaW1wb3J0IFNvbHV0aW9ucyBmcm9tICdAL2NvbXBvbmVudHMvU29sdXRpb25zJ1xuaW1wb3J0IFN0YXRzIGZyb20gJ0AvY29tcG9uZW50cy9TdGF0cydcbmltcG9ydCBDVEEgZnJvbSAnQC9jb21wb25lbnRzL0NUQSdcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gSG9tZSgpIHtcbiAgcmV0dXJuIChcbiAgICA8PlxuICAgICAgPEhlcm8gLz5cbiAgICAgIDxGZWF0dXJlcyAvPlxuICAgICAgPFN0YXRzIC8+XG4gICAgICA8U29sdXRpb25zIC8+XG4gICAgICA8Q1RBIC8+XG4gICAgPC8+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJIZXJvIiwiRmVhdHVyZXMiLCJTb2x1dGlvbnMiLCJTdGF0cyIsIkNUQSIsIkhvbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/page.tsx\n");

/***/ }),

/***/ "(rsc)/./components/CTA.tsx":
/*!****************************!*\
  !*** ./components/CTA.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CTA)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction CTA() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-indigo-700\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-2xl mx-auto text-center py-16 px-4 sm:py-20 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-3xl font-extrabold text-white sm:text-4xl\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"block\",\n                        children: \"Ready to transform your healthcare operations?\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/CTA.tsx\",\n                        lineNumber: 8,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/CTA.tsx\",\n                    lineNumber: 7,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mt-4 text-lg leading-6 text-indigo-200\",\n                    children: \"Join hundreds of healthcare providers who are already experiencing the benefits of AI-powered workflow automation.\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/CTA.tsx\",\n                    lineNumber: 10,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8 flex flex-col sm:flex-row gap-4 justify-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: \"/contact\",\n                            className: \"inline-flex items-center justify-center px-5 py-3 border border-transparent text-base font-medium rounded-md text-indigo-600 bg-white hover:bg-indigo-50 transition-colors\",\n                            children: \"Schedule a Demo\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/CTA.tsx\",\n                            lineNumber: 14,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: \"/features\",\n                            className: \"inline-flex items-center justify-center px-5 py-3 border border-white text-base font-medium rounded-md text-white hover:bg-indigo-600 transition-colors\",\n                            children: \"Explore Features\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/CTA.tsx\",\n                            lineNumber: 20,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/CTA.tsx\",\n                    lineNumber: 13,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/CTA.tsx\",\n            lineNumber: 6,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/CTA.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./components/CTA.tsx\n");

/***/ }),

/***/ "(rsc)/./components/Features.tsx":
/*!*********************************!*\
  !*** ./components/Features.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Features)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction Features() {\n    const features = [\n        {\n            name: \"AI Agentic Workflow Automation\",\n            description: \"Intelligent automation that learns and adapts to your healthcare workflows, reducing manual tasks and improving efficiency.\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"h-6 w-6\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                stroke: \"currentColor\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M13 10V3L4 14h7v7l9-11h-7z\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Features.tsx\",\n                    lineNumber: 8,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Features.tsx\",\n                lineNumber: 7,\n                columnNumber: 9\n            }, this)\n        },\n        {\n            name: \"Comprehensive Patient Profiles\",\n            description: \"Create detailed, longitudinal patient profiles that provide a complete view of patient health history and current status.\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"h-6 w-6\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                stroke: \"currentColor\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Features.tsx\",\n                    lineNumber: 17,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Features.tsx\",\n                lineNumber: 16,\n                columnNumber: 9\n            }, this)\n        },\n        {\n            name: \"Administrative Task Automation\",\n            description: \"Streamline scheduling, billing, documentation, and other administrative tasks with intelligent automation.\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"h-6 w-6\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                stroke: \"currentColor\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Features.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Features.tsx\",\n                lineNumber: 25,\n                columnNumber: 9\n            }, this)\n        },\n        {\n            name: \"Clinician Burden Reduction\",\n            description: \"Free up healthcare professionals to focus on patient care by automating routine administrative and documentation tasks.\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"h-6 w-6\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                stroke: \"currentColor\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Features.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Features.tsx\",\n                lineNumber: 34,\n                columnNumber: 9\n            }, this)\n        },\n        {\n            name: \"Improved Accuracy\",\n            description: \"Reduce human error with AI-powered validation, cross-referencing, and intelligent data verification systems.\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"h-6 w-6\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                stroke: \"currentColor\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Features.tsx\",\n                    lineNumber: 44,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Features.tsx\",\n                lineNumber: 43,\n                columnNumber: 9\n            }, this)\n        },\n        {\n            name: \"Longitudinal Health Data\",\n            description: \"Track patient health trends over time with comprehensive data analysis and predictive insights.\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"h-6 w-6\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                stroke: \"currentColor\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Features.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Features.tsx\",\n                lineNumber: 52,\n                columnNumber: 9\n            }, this)\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"py-12 bg-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"lg:text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-base text-indigo-600 font-semibold tracking-wide uppercase\",\n                            children: \"Features\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Features.tsx\",\n                            lineNumber: 63,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-3xl leading-8 font-extrabold tracking-tight text-gray-900 sm:text-4xl\",\n                            children: \"Transforming Healthcare with AI\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Features.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-4 max-w-2xl text-xl text-gray-500 lg:mx-auto\",\n                            children: \"Our comprehensive platform combines cutting-edge AI technology with healthcare expertise to deliver unprecedented efficiency and accuracy in healthcare operations.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Features.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Features.tsx\",\n                    lineNumber: 62,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-10\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                        className: \"space-y-10 md:space-y-0 md:grid md:grid-cols-2 md:gap-x-8 md:gap-y-10\",\n                        children: features.map((feature)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute flex items-center justify-center h-12 w-12 rounded-md bg-indigo-500 text-white\",\n                                                children: feature.icon\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Features.tsx\",\n                                                lineNumber: 78,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"ml-16 text-lg leading-6 font-medium text-gray-900\",\n                                                children: feature.name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Features.tsx\",\n                                                lineNumber: 81,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Features.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                        className: \"mt-2 ml-16 text-base text-gray-500\",\n                                        children: feature.description\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Features.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, feature.name, true, {\n                                fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Features.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Features.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Features.tsx\",\n                    lineNumber: 73,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Features.tsx\",\n            lineNumber: 61,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Features.tsx\",\n        lineNumber: 60,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./components/Features.tsx\n");

/***/ }),

/***/ "(rsc)/./components/Footer.tsx":
/*!*******************************!*\
  !*** ./components/Footer.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction Footer() {\n    const navigation = {\n        main: [\n            {\n                name: \"About\",\n                href: \"/about\"\n            },\n            {\n                name: \"Features\",\n                href: \"/features\"\n            },\n            {\n                name: \"Solutions\",\n                href: \"/solutions\"\n            },\n            {\n                name: \"Contact\",\n                href: \"/contact\"\n            }\n        ],\n        social: [\n            {\n                name: \"LinkedIn\",\n                href: \"#\",\n                icon: (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        fill: \"currentColor\",\n                        viewBox: \"0 0 20 20\",\n                        ...props,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            fillRule: \"evenodd\",\n                            d: \"M16.338 16.338H13.67V12.16c0-.995-.017-2.277-1.387-2.277-1.39 0-1.601 1.086-1.601 2.207v4.248H8.014v-8.59h2.559v1.174h.037c.356-.675 1.227-1.387 2.526-1.387 2.703 0 3.203 1.778 3.203 4.092v4.711zM5.005 6.575a1.548 1.548 0 11-.003-3.096 1.548 1.548 0 01.003 3.096zm-1.337 9.763H6.34v-8.59H3.667v8.59zM17.668 1H2.328C1.595 1 1 1.581 1 2.298v15.403C1 18.418 1.595 19 2.328 19h15.34c.734 0 1.332-.582 1.332-1.299V2.298C19 1.581 18.402 1 17.668 1z\",\n                            clipRule: \"evenodd\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Footer.tsx\",\n                            lineNumber: 17,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Footer.tsx\",\n                        lineNumber: 16,\n                        columnNumber: 11\n                    }, this)\n            },\n            {\n                name: \"Twitter\",\n                href: \"#\",\n                icon: (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        fill: \"currentColor\",\n                        viewBox: \"0 0 20 20\",\n                        ...props,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            d: \"M6.29 18.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0020 3.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.073 4.073 0 01.8 7.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 010 16.407a11.616 11.616 0 006.29 1.84\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Footer.tsx\",\n                            lineNumber: 30,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Footer.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 11\n                    }, this)\n            }\n        ]\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto py-12 px-4 overflow-hidden sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"-mx-5 -my-2 flex flex-wrap justify-center\",\n                    \"aria-label\": \"Footer\",\n                    children: navigation.main.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-5 py-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: item.href,\n                                className: \"text-base text-gray-500 hover:text-gray-900\",\n                                children: item.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Footer.tsx\",\n                                lineNumber: 43,\n                                columnNumber: 15\n                            }, this)\n                        }, item.name, false, {\n                            fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Footer.tsx\",\n                            lineNumber: 42,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Footer.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8 flex justify-center space-x-6\",\n                    children: navigation.social.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: item.href,\n                            className: \"text-gray-400 hover:text-gray-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"sr-only\",\n                                    children: item.name\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Footer.tsx\",\n                                    lineNumber: 52,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                    className: \"h-6 w-6\",\n                                    \"aria-hidden\": \"true\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Footer.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, item.name, true, {\n                            fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Footer.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Footer.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center space-x-2 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-6 w-6 healthcare-gradient rounded flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white font-bold text-sm\",\n                                        children: \"S\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Footer.tsx\",\n                                        lineNumber: 60,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Footer.tsx\",\n                                    lineNumber: 59,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xl font-bold text-gray-900\",\n                                    children: \"Sahayaka\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Footer.tsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Footer.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-base text-gray-400\",\n                            children: \"\\xa9 2024 Sahayaka. All rights reserved. Transforming healthcare with AI.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Footer.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Footer.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Footer.tsx\",\n            lineNumber: 39,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Footer.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./components/Footer.tsx\n");

/***/ }),

/***/ "(rsc)/./components/Header.tsx":
/*!*******************************!*\
  !*** ./components/Header.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Header.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./components/Hero.tsx":
/*!*****************************!*\
  !*** ./components/Hero.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Hero)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction Hero() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative bg-white overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative z-10 pb-8 bg-white sm:pb-16 md:pb-20 lg:max-w-2xl lg:w-full lg:pb-28 xl:pb-32\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"hidden lg:block absolute right-0 inset-y-0 h-full w-48 text-white transform translate-x-1/2\",\n                            fill: \"currentColor\",\n                            viewBox: \"0 0 100 100\",\n                            preserveAspectRatio: \"none\",\n                            \"aria-hidden\": \"true\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polygon\", {\n                                points: \"50,0 100,0 50,100 0,100\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Hero.tsx\",\n                                lineNumber: 15,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Hero.tsx\",\n                            lineNumber: 8,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                            className: \"mt-10 mx-auto max-w-7xl px-4 sm:mt-12 sm:px-6 md:mt-16 lg:mt-20 lg:px-8 xl:mt-28\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"sm:text-center lg:text-left\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-4xl tracking-tight font-extrabold text-gray-900 sm:text-5xl md:text-6xl\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"block xl:inline\",\n                                                children: \"Revolutionizing\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Hero.tsx\",\n                                                lineNumber: 21,\n                                                columnNumber: 17\n                                            }, this),\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"block text-indigo-600 xl:inline\",\n                                                children: \"Healthcare AI\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Hero.tsx\",\n                                                lineNumber: 22,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Hero.tsx\",\n                                        lineNumber: 20,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-3 text-base text-gray-500 sm:mt-5 sm:text-lg sm:max-w-xl sm:mx-auto md:mt-5 md:text-xl lg:mx-0\",\n                                        children: \"Sahayaka transforms healthcare with AI-powered workflow automation, comprehensive patient profiles, and intelligent administrative task management. Reduce clinician burden while improving accuracy and efficiency.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Hero.tsx\",\n                                        lineNumber: 24,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-5 sm:mt-8 sm:flex sm:justify-center lg:justify-start\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"rounded-md shadow\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: \"/contact\",\n                                                    className: \"w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 md:py-4 md:text-lg md:px-10 transition-colors\",\n                                                    children: \"Get Started\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Hero.tsx\",\n                                                    lineNumber: 30,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Hero.tsx\",\n                                                lineNumber: 29,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-3 sm:mt-0 sm:ml-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: \"/features\",\n                                                    className: \"w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 md:py-4 md:text-lg md:px-10 transition-colors\",\n                                                    children: \"Learn More\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Hero.tsx\",\n                                                    lineNumber: 38,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Hero.tsx\",\n                                                lineNumber: 37,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Hero.tsx\",\n                                        lineNumber: 28,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Hero.tsx\",\n                                lineNumber: 19,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Hero.tsx\",\n                            lineNumber: 18,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Hero.tsx\",\n                    lineNumber: 7,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Hero.tsx\",\n                lineNumber: 6,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:absolute lg:inset-y-0 lg:right-0 lg:w-1/2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-56 w-full bg-gradient-to-br from-indigo-500 via-purple-500 to-pink-500 sm:h-72 md:h-96 lg:w-full lg:h-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center text-white\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-32 h-32 mx-auto mb-4 bg-white bg-opacity-20 rounded-full flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-16 h-16\",\n                                    fill: \"currentColor\",\n                                    viewBox: \"0 0 20 20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        fillRule: \"evenodd\",\n                                        d: \"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm2 2a1 1 0 000 2h.01a1 1 0 100-2H5zm3 0a1 1 0 000 2h3a1 1 0 100-2H8z\",\n                                        clipRule: \"evenodd\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Hero.tsx\",\n                                        lineNumber: 55,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Hero.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Hero.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold\",\n                                children: \"AI-Powered Healthcare\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Hero.tsx\",\n                                lineNumber: 58,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-2 text-sm opacity-90\",\n                                children: \"Intelligent automation for better patient care\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Hero.tsx\",\n                                lineNumber: 59,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Hero.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Hero.tsx\",\n                    lineNumber: 51,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Hero.tsx\",\n                lineNumber: 50,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Hero.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./components/Hero.tsx\n");

/***/ }),

/***/ "(rsc)/./components/Solutions.tsx":
/*!**********************************!*\
  !*** ./components/Solutions.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Solutions)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction Solutions() {\n    const solutions = [\n        {\n            title: \"Hospital Systems\",\n            description: \"Comprehensive workflow automation for large hospital networks, reducing administrative overhead and improving patient flow.\",\n            features: [\n                \"Patient admission automation\",\n                \"Discharge planning\",\n                \"Resource allocation\",\n                \"Staff scheduling\"\n            ],\n            image: \"\\uD83C\\uDFE5\"\n        },\n        {\n            title: \"Clinics & Practices\",\n            description: \"Streamlined operations for smaller healthcare practices, focusing on patient care efficiency and administrative simplification.\",\n            features: [\n                \"Appointment scheduling\",\n                \"Patient records management\",\n                \"Billing automation\",\n                \"Insurance processing\"\n            ],\n            image: \"\\uD83E\\uDE7A\"\n        },\n        {\n            title: \"Specialty Care\",\n            description: \"Specialized solutions for specific medical disciplines with tailored workflows and compliance requirements.\",\n            features: [\n                \"Specialized protocols\",\n                \"Compliance tracking\",\n                \"Research integration\",\n                \"Outcome monitoring\"\n            ],\n            image: \"\\uD83E\\uDDEC\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"py-16 bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-base text-indigo-600 font-semibold tracking-wide uppercase\",\n                            children: \"Solutions\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Solutions.tsx\",\n                            lineNumber: 27,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-3xl leading-8 font-extrabold tracking-tight text-gray-900 sm:text-4xl\",\n                            children: \"Tailored for Every Healthcare Setting\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Solutions.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-4 max-w-2xl text-xl text-gray-500 mx-auto\",\n                            children: \"From large hospital systems to specialized clinics, our AI platform adapts to your unique needs and workflows.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Solutions.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Solutions.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-12 space-y-10 sm:space-y-0 sm:grid sm:grid-cols-1 sm:gap-x-6 sm:gap-y-12 lg:grid-cols-3 lg:gap-x-8\",\n                    children: solutions.map((solution)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-hover bg-white rounded-lg shadow-md overflow-hidden\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-4xl mb-4\",\n                                            children: solution.image\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Solutions.tsx\",\n                                            lineNumber: 40,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg leading-6 font-medium text-gray-900 mb-2\",\n                                            children: solution.title\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Solutions.tsx\",\n                                            lineNumber: 41,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-base text-gray-500 mb-4\",\n                                            children: solution.description\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Solutions.tsx\",\n                                            lineNumber: 44,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2\",\n                                            children: solution.features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center text-sm text-gray-600\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"h-4 w-4 text-green-500 mr-2\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Solutions.tsx\",\n                                                                lineNumber: 51,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Solutions.tsx\",\n                                                            lineNumber: 50,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        feature\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Solutions.tsx\",\n                                                    lineNumber: 49,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Solutions.tsx\",\n                                            lineNumber: 47,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Solutions.tsx\",\n                                    lineNumber: 39,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-6 py-4 bg-gray-50\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"w-full bg-indigo-600 text-white py-2 px-4 rounded-md hover:bg-indigo-700 transition-colors\",\n                                        children: \"Learn More\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Solutions.tsx\",\n                                        lineNumber: 59,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Solutions.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, solution.title, true, {\n                            fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Solutions.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Solutions.tsx\",\n                    lineNumber: 36,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Solutions.tsx\",\n            lineNumber: 25,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Solutions.tsx\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./components/Solutions.tsx\n");

/***/ }),

/***/ "(rsc)/./components/Stats.tsx":
/*!******************************!*\
  !*** ./components/Stats.tsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Stats)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction Stats() {\n    const stats = [\n        {\n            id: 1,\n            name: \"Reduction in Administrative Time\",\n            value: \"75%\"\n        },\n        {\n            id: 2,\n            name: \"Improvement in Data Accuracy\",\n            value: \"95%\"\n        },\n        {\n            id: 3,\n            name: \"Faster Patient Processing\",\n            value: \"3x\"\n        },\n        {\n            id: 4,\n            name: \"Healthcare Providers Served\",\n            value: \"500+\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-indigo-800\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto py-12 px-4 sm:py-16 sm:px-6 lg:px-8 lg:py-20\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl font-extrabold text-white sm:text-4xl\",\n                            children: \"Proven Results in Healthcare Innovation\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Stats.tsx\",\n                            lineNumber: 13,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-3 text-xl text-indigo-200 sm:mt-4\",\n                            children: \"Our AI-powered platform delivers measurable improvements across all aspects of healthcare administration.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Stats.tsx\",\n                            lineNumber: 16,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Stats.tsx\",\n                    lineNumber: 12,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                    className: \"mt-10 text-center sm:max-w-3xl sm:mx-auto sm:grid sm:grid-cols-2 sm:gap-8 lg:grid-cols-4\",\n                    children: stats.map((stat)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                    className: \"order-2 mt-2 text-lg leading-6 font-medium text-indigo-200\",\n                                    children: stat.name\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Stats.tsx\",\n                                    lineNumber: 23,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                    className: \"order-1 text-5xl font-extrabold text-white\",\n                                    children: stat.value\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Stats.tsx\",\n                                    lineNumber: 26,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, stat.id, true, {\n                            fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Stats.tsx\",\n                            lineNumber: 22,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Stats.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Stats.tsx\",\n            lineNumber: 11,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/NandiWork/repos/sahayaka/apps/websites/sahayaka.ai/components/Stats.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9jb21wb25lbnRzL1N0YXRzLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQWUsU0FBU0E7SUFDdEIsTUFBTUMsUUFBUTtRQUNaO1lBQUVDLElBQUk7WUFBR0MsTUFBTTtZQUFvQ0MsT0FBTztRQUFNO1FBQ2hFO1lBQUVGLElBQUk7WUFBR0MsTUFBTTtZQUFnQ0MsT0FBTztRQUFNO1FBQzVEO1lBQUVGLElBQUk7WUFBR0MsTUFBTTtZQUE2QkMsT0FBTztRQUFLO1FBQ3hEO1lBQUVGLElBQUk7WUFBR0MsTUFBTTtZQUErQkMsT0FBTztRQUFPO0tBQzdEO0lBRUQscUJBQ0UsOERBQUNDO1FBQUlDLFdBQVU7a0JBQ2IsNEVBQUNEO1lBQUlDLFdBQVU7OzhCQUNiLDhEQUFDRDtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNDOzRCQUFHRCxXQUFVO3NDQUFpRDs7Ozs7O3NDQUcvRCw4REFBQ0U7NEJBQUVGLFdBQVU7c0NBQXVDOzs7Ozs7Ozs7Ozs7OEJBSXRELDhEQUFDRztvQkFBR0gsV0FBVTs4QkFDWEwsTUFBTVMsR0FBRyxDQUFDLENBQUNDLHFCQUNWLDhEQUFDTjs0QkFBa0JDLFdBQVU7OzhDQUMzQiw4REFBQ007b0NBQUdOLFdBQVU7OENBQ1hLLEtBQUtSLElBQUk7Ozs7Ozs4Q0FFWiw4REFBQ1U7b0NBQUdQLFdBQVU7OENBQ1hLLEtBQUtQLEtBQUs7Ozs7Ozs7MkJBTExPLEtBQUtULEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQWE3QiIsInNvdXJjZXMiOlsid2VicGFjazovL3NhaGF5YWthLy4vY29tcG9uZW50cy9TdGF0cy50c3g/ZDAwMCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBTdGF0cygpIHtcbiAgY29uc3Qgc3RhdHMgPSBbXG4gICAgeyBpZDogMSwgbmFtZTogJ1JlZHVjdGlvbiBpbiBBZG1pbmlzdHJhdGl2ZSBUaW1lJywgdmFsdWU6ICc3NSUnIH0sXG4gICAgeyBpZDogMiwgbmFtZTogJ0ltcHJvdmVtZW50IGluIERhdGEgQWNjdXJhY3knLCB2YWx1ZTogJzk1JScgfSxcbiAgICB7IGlkOiAzLCBuYW1lOiAnRmFzdGVyIFBhdGllbnQgUHJvY2Vzc2luZycsIHZhbHVlOiAnM3gnIH0sXG4gICAgeyBpZDogNCwgbmFtZTogJ0hlYWx0aGNhcmUgUHJvdmlkZXJzIFNlcnZlZCcsIHZhbHVlOiAnNTAwKycgfSxcbiAgXVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJiZy1pbmRpZ28tODAwXCI+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTd4bCBteC1hdXRvIHB5LTEyIHB4LTQgc206cHktMTYgc206cHgtNiBsZzpweC04IGxnOnB5LTIwXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctNHhsIG14LWF1dG8gdGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC0zeGwgZm9udC1leHRyYWJvbGQgdGV4dC13aGl0ZSBzbTp0ZXh0LTR4bFwiPlxuICAgICAgICAgICAgUHJvdmVuIFJlc3VsdHMgaW4gSGVhbHRoY2FyZSBJbm5vdmF0aW9uXG4gICAgICAgICAgPC9oMj5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJtdC0zIHRleHQteGwgdGV4dC1pbmRpZ28tMjAwIHNtOm10LTRcIj5cbiAgICAgICAgICAgIE91ciBBSS1wb3dlcmVkIHBsYXRmb3JtIGRlbGl2ZXJzIG1lYXN1cmFibGUgaW1wcm92ZW1lbnRzIGFjcm9zcyBhbGwgYXNwZWN0cyBvZiBoZWFsdGhjYXJlIGFkbWluaXN0cmF0aW9uLlxuICAgICAgICAgIDwvcD5cbiAgICAgICAgPC9kaXY+XG4gICAgICAgIDxkbCBjbGFzc05hbWU9XCJtdC0xMCB0ZXh0LWNlbnRlciBzbTptYXgtdy0zeGwgc206bXgtYXV0byBzbTpncmlkIHNtOmdyaWQtY29scy0yIHNtOmdhcC04IGxnOmdyaWQtY29scy00XCI+XG4gICAgICAgICAge3N0YXRzLm1hcCgoc3RhdCkgPT4gKFxuICAgICAgICAgICAgPGRpdiBrZXk9e3N0YXQuaWR9IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2xcIj5cbiAgICAgICAgICAgICAgPGR0IGNsYXNzTmFtZT1cIm9yZGVyLTIgbXQtMiB0ZXh0LWxnIGxlYWRpbmctNiBmb250LW1lZGl1bSB0ZXh0LWluZGlnby0yMDBcIj5cbiAgICAgICAgICAgICAgICB7c3RhdC5uYW1lfVxuICAgICAgICAgICAgICA8L2R0PlxuICAgICAgICAgICAgICA8ZGQgY2xhc3NOYW1lPVwib3JkZXItMSB0ZXh0LTV4bCBmb250LWV4dHJhYm9sZCB0ZXh0LXdoaXRlXCI+XG4gICAgICAgICAgICAgICAge3N0YXQudmFsdWV9XG4gICAgICAgICAgICAgIDwvZGQ+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApKX1cbiAgICAgICAgPC9kbD5cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApXG59XG4iXSwibmFtZXMiOlsiU3RhdHMiLCJzdGF0cyIsImlkIiwibmFtZSIsInZhbHVlIiwiZGl2IiwiY2xhc3NOYW1lIiwiaDIiLCJwIiwiZGwiLCJtYXAiLCJzdGF0IiwiZHQiLCJkZCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./components/Stats.tsx\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fnandi%2FDocuments%2FNandiWork%2Frepos%2Fsahayaka%2Fapps%2Fwebsites%2Fsahayaka.ai%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fnandi%2FDocuments%2FNandiWork%2Frepos%2Fsahayaka%2Fapps%2Fwebsites%2Fsahayaka.ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();