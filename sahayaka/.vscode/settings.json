{"typescript.preferences.includePackageJsonAutoImports": "on", "typescript.suggest.autoImports": true, "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "files.associations": {"*.css": "tailwindcss"}, "emmet.includeLanguages": {"javascript": "javascriptreact", "typescript": "typescriptreact"}, "tailwindCSS.includeLanguages": {"typescript": "typescript", "typescriptreact": "typescriptreact"}, "tailwindCSS.experimental.configFile": {"apps/websites/sahayaka.ai/tailwind.config.js": "apps/websites/sahayaka.ai/**"}, "search.exclude": {"**/node_modules": true, "**/dist": true, "**/.next": true, "**/out": true, "**/.cache": true}, "files.exclude": {"**/node_modules": true, "**/.next": true, "**/out": true}}